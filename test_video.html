<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            width: 100%;
            height: 400px;
            position: relative;
            background: #000;
            border: 2px solid red;
        }
        .test-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <h1>Video Background Test</h1>
    <p>Testing if the video file loads correctly:</p>
    
    <div class="test-container">
        <video class="test-video" autoplay muted loop playsinline controls>
            <source src="resources/videos/index-video.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>
    
    <script>
        const video = document.querySelector('.test-video');
        
        video.addEventListener('loadeddata', function() {
            console.log('Test video loaded successfully');
        });
        
        video.addEventListener('error', function(e) {
            console.error('Test video failed to load:', e);
            console.error('Video error:', video.error);
        });
        
        // Test video file accessibility
        fetch('resources/videos/index-video.mp4', { method: 'HEAD' })
            .then(response => {
                console.log('Video file response:', response.status, response.statusText);
            })
            .catch(error => {
                console.error('Error accessing video file:', error);
            });
    </script>
</body>
</html>
