/**
 * Banner Video Background Handler
 * Manages video loading, playback, and performance optimization
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Banner video script loaded');

    const video = document.querySelector('.banner-video-background');

    if (!video) {
        console.warn('Banner video element not found');
        return;
    }

    console.log('Banner video element found:', video);
    const source = video.querySelector('source');
    console.log('Video source element:', source);
    console.log('Video src:', source?.src);

    // Test if video file exists by creating a test request
    if (source && source.src) {
        fetch(source.src, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    console.log('Video file exists and is accessible');
                } else {
                    console.error('Video file not accessible:', response.status);
                }
            })
            .catch(error => {
                console.error('Error checking video file:', error);
            });
    }

    // Force video to be visible and properly positioned
    video.style.display = 'block';
    video.style.position = 'absolute';
    video.style.top = '0';
    video.style.left = '0';
    video.style.width = '100%';
    video.style.height = '100%';
    video.style.objectFit = 'cover';
    video.style.zIndex = '1';

    // Handle video loading states
    video.addEventListener('loadeddata', function() {
        video.setAttribute('data-loaded', 'true');
        console.log('Banner video loaded successfully');
    });

    // Handle video loading errors
    video.addEventListener('error', function(e) {
        console.error('Banner video failed to load:', e);
        console.error('Video error details:', video.error);
        video.style.display = 'none';
        // Fallback background will be visible via CSS
    });
    
    // Ensure video plays (some browsers may block autoplay)
    video.addEventListener('canplay', function() {
        console.log('Video can play, attempting to start playback');
        const playPromise = video.play();

        if (playPromise !== undefined) {
            playPromise.then(function() {
                console.log('Video playback started successfully');
            }).catch(function(error) {
                console.warn('Video autoplay was prevented:', error);
                // Video will remain paused, which is acceptable
            });
        }
    });

    // Force play attempt after a short delay
    setTimeout(function() {
        if (video.paused) {
            console.log('Attempting to force video playback');
            video.play().catch(function(error) {
                console.warn('Forced video play failed:', error);
            });
        }
    }, 1000);
    
    // Performance optimization: pause video when page is not visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            video.pause();
        } else {
            video.play().catch(function(error) {
                console.warn('Could not resume video playback:', error);
            });
        }
    });
    
    // Intersection Observer for performance optimization
    // Only play video when banner is in viewport
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    video.play().catch(function(error) {
                        console.warn('Could not start video playback:', error);
                    });
                } else {
                    video.pause();
                }
            });
        }, {
            threshold: 0.1 // Play when 10% of video is visible
        });
        
        observer.observe(video);
    }
    
    // Handle reduced motion preference
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        video.pause();
        video.style.display = 'none';
        console.log('Video disabled due to reduced motion preference');
    }
    
    // Mobile optimization: reduce video quality on slow connections
    if ('connection' in navigator) {
        const connection = navigator.connection;
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
            video.pause();
            video.style.display = 'none';
            console.log('Video disabled due to slow connection');
        }
    }
});
