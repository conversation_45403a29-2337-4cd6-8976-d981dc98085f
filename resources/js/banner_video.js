/**
 * Banner Video Background Handler
 * Manages video loading, playback, and performance optimization
 */

document.addEventListener('DOMContentLoaded', function() {
    const video = document.querySelector('.banner-video-background');
    
    if (!video) return;
    
    // Handle video loading states
    video.addEventListener('loadeddata', function() {
        video.setAttribute('data-loaded', 'true');
        console.log('Banner video loaded successfully');
    });
    
    // Handle video loading errors
    video.addEventListener('error', function(e) {
        console.warn('Banner video failed to load:', e);
        video.style.display = 'none';
        // Fallback background will be visible via CSS
    });
    
    // Ensure video plays (some browsers may block autoplay)
    video.addEventListener('canplay', function() {
        const playPromise = video.play();
        
        if (playPromise !== undefined) {
            playPromise.catch(function(error) {
                console.warn('Video autoplay was prevented:', error);
                // Video will remain paused, which is acceptable
            });
        }
    });
    
    // Performance optimization: pause video when page is not visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            video.pause();
        } else {
            video.play().catch(function(error) {
                console.warn('Could not resume video playback:', error);
            });
        }
    });
    
    // Intersection Observer for performance optimization
    // Only play video when banner is in viewport
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    video.play().catch(function(error) {
                        console.warn('Could not start video playback:', error);
                    });
                } else {
                    video.pause();
                }
            });
        }, {
            threshold: 0.1 // Play when 10% of video is visible
        });
        
        observer.observe(video);
    }
    
    // Handle reduced motion preference
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        video.pause();
        video.style.display = 'none';
        console.log('Video disabled due to reduced motion preference');
    }
    
    // Mobile optimization: reduce video quality on slow connections
    if ('connection' in navigator) {
        const connection = navigator.connection;
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
            video.pause();
            video.style.display = 'none';
            console.log('Video disabled due to slow connection');
        }
    }
});
