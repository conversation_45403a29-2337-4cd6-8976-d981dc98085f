/* Banner Video Background Styles - Maximum Override Power */

/* Override ALL possible theme backgrounds */
.banner__area.banner-7,
.banner__area.banner-7::before,
.banner__area.banner-7::after,
.banner-7,
.banner-7::before,
.banner-7::after {
    background: none !important;
    background-image: none !important;
    background-color: transparent !important;
}

/* Force video background container with maximum specificity */
.banner__area.banner-7 .banner-7__top__wrapper {
    position: relative !important;
    min-height: 100vh !important;
    overflow: hidden !important;
    background: none !important;
    background-image: none !important;
    background-color: transparent !important;
}

/* Video background styling with maximum specificity */
.banner__area.banner-7 .banner-7__top__wrapper .banner-video-background {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    z-index: 100 !important;
    pointer-events: none !important;
    display: block !important; /* Force display */
}

/* Semi-transparent overlay for text readability */
.banner__area.banner-7 .banner-7__top__wrapper .banner-video-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 101 !important;
    pointer-events: none !important;
}

/* Ensure content appears above video and overlay */
.banner__area.banner-7 .banner-7__top {
    position: relative !important;
    z-index: 102 !important; /* Higher z-index to ensure visibility */
}

/* Enhance text contrast for better readability over video */
.banner__area.banner-7 .banner-7__title {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
    color: white !important;
}

.banner__area.banner-7 .banner-7__dec {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
    color: white !important;
}

/* Ensure buttons remain interactive and visible */
.banner__area.banner-7 .banner-7__btn__wrapper {
    position: relative !important;
    z-index: 103 !important;
}

/* Responsive Design with High Specificity */
@media (max-width: 768px) {
    .banner__area.banner-7 .banner-7__top__wrapper {
        min-height: 80vh !important;
    }

    /* Increase overlay opacity on mobile for better text readability */
    .banner__area.banner-7 .banner-7__top__wrapper .banner-video-overlay {
        background: rgba(0, 0, 0, 0.6) !important;
    }

    /* Enhanced text shadow on mobile */
    .banner__area.banner-7 .banner-7__title {
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
    }

    .banner__area.banner-7 .banner-7__dec {
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
    }
}

@media (max-width: 480px) {
    .banner__area.banner-7 .banner-7__top__wrapper {
        min-height: 70vh !important;
    }

    /* Maximum overlay opacity for small screens */
    .banner__area.banner-7 .banner-7__top__wrapper .banner-video-overlay {
        background: rgba(0, 0, 0, 0.7) !important;
    }
}

/* Fallback styles if video fails to load */
.banner__area.banner-7 .banner-video-background:not([src]) {
    display: none !important;
}

/* Ensure video doesn't show controls or interfere with accessibility */
.banner__area.banner-7 .banner-video-background::-webkit-media-controls {
    display: none !important;
}

.banner__area.banner-7 .banner-video-background::-webkit-media-controls-panel {
    display: none !important;
}

/* Performance optimization - reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .banner__area.banner-7 .banner-video-background {
        display: none !important;
    }

    /* Provide a static background when motion is reduced */
    .banner__area.banner-7 .banner-7__top__wrapper {
        background: linear-gradient(135deg, var(--azul-oscuro-empresarial, #1496D7) 0%, var(--azul-claro-empresarial, #6FA4C4) 100%) !important;
    }
}

/* Ensure proper aspect ratio maintenance */
@media (orientation: portrait) {
    .banner__area.banner-7 .banner-video-background {
        object-position: center center !important;
    }
}

@media (orientation: landscape) {
    .banner__area.banner-7 .banner-video-background {
        object-position: center center !important;
    }
}

/* Loading state - show background color while video loads */
.banner__area.banner-7 .banner-7__top__wrapper::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, var(--azul-oscuro-empresarial, #1496D7) 0%, var(--azul-claro-empresarial, #6FA4C4) 100%) !important;
    z-index: 0 !important;
}

/* Hide the loading background once video is loaded and playing */
.banner__area.banner-7 .banner-video-background[data-loaded="true"] ~ .banner-7__top__wrapper::before {
    display: none !important;
}

/* NUCLEAR OPTION - Override ALL possible theme backgrounds */
.banner__area.banner-7,
.banner__area.banner-7 *:not(.banner-video-background):not(.banner-video-overlay),
.banner-7,
.banner-7 *:not(.banner-video-background):not(.banner-video-overlay),
section.banner__area,
section.banner-7,
div.banner-7__top__wrapper {
    background-image: none !important;
    background-color: transparent !important;
    background: none !important;
}

/* Force remove any pseudo-element backgrounds from theme */
.banner__area.banner-7::before,
.banner__area.banner-7::after,
.banner-7::before,
.banner-7::after,
.banner-7__top__wrapper::before,
.banner-7__top__wrapper::after {
    display: none !important;
    content: none !important;
    background: none !important;
}

/* Ensure the banner section itself doesn't have conflicting backgrounds */
.banner__area.banner-7 {
    background: transparent !important;
}

/* Ensure video element is properly sized and visible */
.banner-video-background {
    min-width: 100px !important;
    min-height: 100px !important;
}

/* Force override any conflicting styles from theme */
.banner__area.banner-7 .banner-7__top__wrapper .banner-video-background {
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
}

/* Ensure shape doesn't interfere with video */
.banner__area.banner-7 .banner-7__top__shape-1 {
    z-index: 0 !important;
    display: none !important;
}

/* FINAL OVERRIDE - Use highest specificity possible */
html body .banner__area.banner-7.overflow-hidden {
    background: transparent !important;
    background-image: none !important;
    background-color: transparent !important;
}

html body .banner__area.banner-7.overflow-hidden .banner-7__top__wrapper {
    background: transparent !important;
    background-image: none !important;
    background-color: transparent !important;
}

/* Remove any gradients or backgrounds that might be applied by theme */
html body .banner__area.banner-7.overflow-hidden,
html body .banner__area.banner-7.overflow-hidden * {
    background-attachment: initial !important;
    background-blend-mode: initial !important;
    background-clip: initial !important;
    background-origin: initial !important;
    background-position: initial !important;
    background-repeat: initial !important;
    background-size: initial !important;
}
