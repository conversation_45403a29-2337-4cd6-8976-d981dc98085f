/* Banner Video Background Styles */

/* Container positioning for video background */
.banner-7__top__wrapper {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
}

/* Video background styling */
.banner-video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
    pointer-events: none; /* Prevent video from interfering with content interaction */
}

/* Semi-transparent overlay for text readability */
.banner-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Dark semi-transparent overlay */
    z-index: 2;
    pointer-events: none; /* Allow clicks to pass through to content */
}

/* Ensure content appears above video and overlay */
.banner-7__top {
    position: relative;
    z-index: 3;
}

/* Enhance text contrast for better readability over video */
.banner-7__title {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.banner-7__dec {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Ensure buttons remain interactive and visible */
.banner-7__btn__wrapper {
    position: relative;
    z-index: 4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-7__top__wrapper {
        min-height: 80vh; /* Slightly smaller on mobile for better UX */
    }
    
    /* Increase overlay opacity on mobile for better text readability */
    .banner-video-overlay {
        background: rgba(0, 0, 0, 0.6);
    }
    
    /* Enhanced text shadow on mobile */
    .banner-7__title {
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9);
    }
    
    .banner-7__dec {
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
    }
}

@media (max-width: 480px) {
    .banner-7__top__wrapper {
        min-height: 70vh; /* Even smaller on very small screens */
    }
    
    /* Maximum overlay opacity for small screens */
    .banner-video-overlay {
        background: rgba(0, 0, 0, 0.7);
    }
}

/* Fallback styles if video fails to load */
.banner-video-background:not([src]) {
    display: none;
}

/* Ensure video doesn't show controls or interfere with accessibility */
.banner-video-background::-webkit-media-controls {
    display: none !important;
}

.banner-video-background::-webkit-media-controls-panel {
    display: none !important;
}

/* Performance optimization - reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .banner-video-background {
        display: none;
    }
    
    /* Provide a static background when motion is reduced */
    .banner-7__top__wrapper {
        background: linear-gradient(135deg, var(--azul-oscuro-empresarial, #1496D7) 0%, var(--azul-claro-empresarial, #6FA4C4) 100%);
    }
}

/* Ensure proper aspect ratio maintenance */
@media (orientation: portrait) {
    .banner-video-background {
        object-position: center center;
    }
}

@media (orientation: landscape) {
    .banner-video-background {
        object-position: center center;
    }
}

/* Loading state - show background color while video loads */
.banner-7__top__wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--azul-oscuro-empresarial, #1496D7) 0%, var(--azul-claro-empresarial, #6FA4C4) 100%);
    z-index: 0;
}

/* Hide the loading background once video is loaded and playing */
.banner-video-background[data-loaded="true"] ~ .banner-7__top__wrapper::before {
    display: none;
}
